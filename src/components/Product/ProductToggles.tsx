'use client'

/* eslint-disable complexity */
import ProductLink from './ProductLink'
import ProductPrice from './ProductPrice'

import Typography from '../Typography'
import RenderIf from '../Generic/RenderIf'
import Callouts from '../Generic/Callout/Callouts'
import { CalloutProps } from '../Generic/Callout/types'
import ArrowUpIcon from '../Generic/Icon/lib/ArrowUp'
import RenderCTAS from '../Generic/RenderCTAS'

import Callout from '@/components/Generic/Callout'
import Container from '@components/layout/Container'
import {
  colors,
  fontSizes,
  spacing,
  globalTokens as $
} from '@/app/themeTokens.stylex'
import { toCamelCase } from '@utils/regex'
import { ThemeColors } from '@/app/themeThemes.stylex'

import * as stylex from '@stylexjs/stylex'
import { useMemo, useState } from 'react'

import type { ProductGroup } from './types'

const styles = stylex.create({
  wrapper: {
    alignItems: 'stretch',
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: colors.navy,
    borderRadius: $.borderRadiusSmall,
    marginBlockStart: {
      default: spacing.sm,
      '@media (min-width: 1024px)': spacing.md
    },
    marginBlockEnd: {
      default: spacing.sm,
      '@media (min-width: 1024px)': spacing.md
    },
  },
  wrapperLayout2: {
    borderWidth: null,
    borderStyle: null,
    borderColor: null,
    borderRadius: null,
    marginBottom: spacing.xs,
    marginTop: spacing.md,
  },
  mainToggleWrapper: {
    borderWidth: null,
    borderStyle: null,
    borderColor: null,
    borderRadius: null,
    flexDirection: 'column',
    gap: spacing.sm,
  },
  toggleItem: {
    flex: '1',
    position: 'relative',
    borderRightWidth: {
      default: 1,
      ':last-child': 0,
    },
    borderRightStyle: 'solid',
    borderRightColor: colors.navy,
  },
  toggleItemLayout2: {
    borderRightWidth: null,
    borderRightStyle: null,
    borderRightColor: null,
    backgroundColor: {
      default: colors.offWhite,
      ':hover': colors.gray100,
    },
  },
  setTitle: {
    alignItems: 'flex-end',
    paddingBlockStart: {
      '@media(min-width: 992px)': '28px'
    },
    gap: {
      default: '6px',
      '@media (min-width: 1024px)': spacing.xxs
    },
  },
  setTitleDesktop: {
    display: {
      default: 'none',
      '@media (min-width: 1024px)': 'flex',
    }
  },
  mainToggleItem: {
    borderRightWidth: null,
    borderRightStyle: null,
    borderRightColor: null,
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: colors.gray300,
    borderRadius: $.borderRadiusSmall,
    backgroundColor: {
      default: colors.offWhite,
      ':hover': colors.gray100,
    },
  },
  link: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    paddingInline: {
      default: spacing.xxs,
      '@media (min-width: 1024px)': spacing.md,
    },
    paddingBlock: {
      default: spacing.sm,
      '@media (min-width: 1024px)': spacing.md,
    },
    fontSize: fontSizes.xs,
    textAlign: 'center',
    position: 'relative',
  },
  mainToggleLink: {
    fontWeight: 'bold',
    alignItems: 'start',
    flexDirection: 'column',
    gap: spacing.xxs,
    paddingInline: {
      default: spacing.sm,
      '@media (min-width: 1024px)': spacing.md,
    },
  },
  dialogTrigger: {
    padding: 0,
    fontWeight: 400
  },
  linkActive: {
    fontWeight: 'bold',
    outline: `1px solid ${colors.navy}`,
    backgroundColor: colors.gray100,
  },
  linkLayout2: {
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: colors.gray300,
    borderRadius: $.borderRadiusSmall,
    paddingBlock: spacing.sm,
  },
  linkActiveLayout2: {
    outline: null,
    borderWidth: '1px',
    borderColor: colors.navy,
  },
  mainToggleActiveLink: {
    borderColor: colors.navy,
    borderRadius: $.borderRadiusSmall,
    cursor: 'pointer',
    outline: `1px solid ${colors.navy}`,
  },
  wrapperTitleContent: { width: 'fit-content' },
  mainToggleWrapperTitleContent: { width: '100%' },
  desktopTogglesV1: {
    display: {
      default: 'none',
      '@media (min-width: 1024px)': 'flex',
    }
  },
  desktopTogglesV2: {
    display: {
      default: 'none',
      '@media (min-width: 1024px)': 'grid',
    }
  },
  wrapperPrices: {
    flexDirection: 'row-reverse',
    fontWeight: 400,
  },
  wrapperPricesLayout2: {
    position: 'absolute',
    top: '50%',
    right: {
      default: spacing.sm,
      '@media (min-width: 1024px)': spacing.md,
    },
    height: 'fit-content',
  },
  productPriceSize: { fontSize: '14px' },
  arrowSize: {
    width: 12,
    height: 8,
    transition: 'transform 0.3s ease-in-out',
  },
  activeArrow: { transform: 'rotate(180deg)' },
  subheaderColor: { color: colors.gray },
  wrapperExtraInfo: {
    flexDirection: 'column',
    flexWrap: 'nowrap',
    backgroundColor: colors.offWhite,
    borderRadius: $.borderRadiusSmall,
    paddingInline: {
      default: spacing.md,
      '@media (min-width: 1024px)': spacing.lg,
    },
    paddingBlock: 0,
    maxHeight: 0,
    overflow: 'hidden',
    transition: 'max-height 0.3s ease-in-out, padding 0.3s ease-in-out',
  },
  wrapperExtraInfoActive: {
    maxHeight: '500px',
    paddingBlock: {
      default: spacing.sm,
      '@media (min-width: 1024px)': spacing.md,
    },
  },
  extraInfo: (theme: keyof typeof colors) => ({
    color: colors[theme],
  }),
  calloutStyleProp: {
    textAlign: 'center',
    position: 'absolute',
    pointerEvents: 'none',
    top: 0,
    left: '50%',
    transform: 'translateX(-50%)',
    zIndex: 1,

    minWidth: {
      default: '90px',
      '@media (min-width: 768px)': '0px'
    },
    display: {
      default: 'none',
      '@media (min-width: 375px)': 'block'
    }
  },
  badgeStyleProp: {
    paddingInline: '4px',
    whiteSpace: 'nowrap',
  },
  hasCallout: {
    flex: '1',
    display: 'grid',
  },
  isCalloutWithCallout: {
    gridArea: '1/1'
  },
  isCallout: {
    gridArea: '1/1',
    width: 'fit-content',
    placeSelf: 'start center',
    translate: '0 -50%'
  },
  grid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(4, 1fr)',
    gridTemplateRows: '1fr',
    gap: '.5rem',
  },
  grid3: {
    gridTemplateColumns: 'repeat(3, 1fr)',
  },
  grid4: {
    gridTemplateColumns: 'repeat(4, 1fr)',
  },
  span1: {
    gridColumn: 'span 1',
  },
  span2: {
    gridColumn: 'span 2',
  },
  spacing: {
    marginRight: 'auto'
  },
  gray: {
    color: colors.gray
  }
})

const MIN_PRODUCTS = 2

type ProductTogglesProps = {
  products: ProductGroup['products']['items']
  productSlug: string
  group?: string
  desktop?: boolean
  selectedVariant?: any
  testVariant?: string
  openDialog?: {
    text: string
    page: {
      text: string
      sys: {
        id: string
      }
    }
  }
  togglesV2?: boolean
};

const ProductToggles = ({
  products,
  productSlug,
  group,
  desktop,
  selectedVariant,
  openDialog,
  togglesV2 = false,
}: ProductTogglesProps) => {
  const [activeStates, setActiveStates] = useState(
    products.map((product) => productSlug === product.slug)
  )
  const [activeTitle, setActiveTitle] = useState(
    products.find((product) => productSlug === product.slug)?.title || ''
  )

  const hasToggle = useState(products?.[0]?.groups?.items?.[0].settings?.layout === 'Layout 1')[0]

  const TWO = 2
  const THREE = 3
  const FOUR = 4
  const FIVE = 5

  const { gridStyle, itemStyle } = useMemo(() => {
    const { length } = products

    if (length === 1) {
      return { gridStyle: styles.grid4, itemStyle: styles.span1 }
    }
    if (length === TWO) {
      return { gridStyle: styles.grid4, itemStyle: styles.span2 }
    }
    if (length % THREE === 0) {
      return { gridStyle: styles.grid3, itemStyle: styles.span1 }
    }
    if (length % FOUR === 0) {
      return { gridStyle: styles.grid4, itemStyle: styles.span2 }
    }
    if (length === FIVE) {
      return { gridStyle: styles.grid3, itemStyle: styles.span1 }
    }

    return { gridStyle: styles.grid4, itemStyle: styles.span1 }
  }, [products.length])

  if (!products || products.length < MIN_PRODUCTS) {
    return null
  }

  const handleToggle = (index: number) => {
    if (activeStates[index]) return

    setActiveStates((prevStates) => {
      const newStates = [...prevStates]
      newStates[index] = !newStates[index]

      if (newStates[index]!) {
        setActiveTitle(products[index].title)
      } else {
        setActiveTitle('')
      }

      return newStates
    })
  }

  const enableToggles = true
  const enableTogglesV2 = togglesV2

  const activeProduct = products.find((product) => productSlug === product.slug)
  const activeProductToggleGroup = activeProduct?.groups?.items.find((item) => item.slug === group)
  const activeProductToggleGroupCurr = activeProductToggleGroup?.groupItem?.items.find((groupItem: any) => groupItem.product.slug === activeProduct?.slug)

  return (
    <>
      {enableToggles && (
        <Container flex flexRow styleProp={[styles.setTitle, desktop && styles.setTitleDesktop]}>
          <Typography
            as="p"
            typographyTheme="bodyLarge"
            fontBold
          >
            Select Product:
          </Typography>
          <Typography
            as="p"
            typographyTheme="bodySmall"
            styleProp={[
              styles.spacing,
              styles.gray
            ]}
          >
            {activeProductToggleGroupCurr?.title || activeTitle}
          </Typography>
          {openDialog && (
            <Typography
              as="p"
              typographyTheme="captionLarge"
            >
              <RenderCTAS
                buttons={[
                  {
                    children: openDialog.text,
                    id: openDialog.page.sys.id,
                    styleProp: styles.dialogTrigger,
                    variant: 'underlined'
                  },
                ]}
              />
            </Typography>
          )}
        </Container>
      )}
      <Container
        as="div"
        flex
        flexRow
        styleProp={[
          styles.wrapper,
          enableTogglesV2 && styles.grid,
          enableTogglesV2 && gridStyle,
          enableToggles && styles.wrapperLayout2,
          enableToggles && hasToggle && styles.mainToggleWrapper,
          desktop && !togglesV2 && styles.desktopTogglesV1,
          desktop && togglesV2 && styles.desktopTogglesV2,
        ]}
      >
        {products.map((product, index) => {
          const isActive = productSlug === product.slug
          const toogleActive = activeStates[index]
          // Always include group parameter when we're in a grouped context (not 'default')
          // This ensures navigation between products maintains the group context
          const addGroupParam = group && group !== 'default'

          const atfToggleGroup = product.groups?.items?.find((item) => item.slug === group)
          const atfToggleCurr = atfToggleGroup?.groupItem?.items?.find((groupItem: any) => groupItem.product.slug === product.slug)
          const atfToggle = atfToggleCurr || null
          const callouts: CalloutProps[] = []

          if (atfToggle?.callout) callouts.push(atfToggle.callout)
          const callout = callouts[0]

          const fallbackVariant = product?.variants?.items?.[0]
          const toggleVariant = isActive ? selectedVariant : fallbackVariant

          const price = toggleVariant?.price ?? 0
          const compareAtPrice = toggleVariant?.compareAtPrice ?? 0

          return (
            <div
              key={product.slug}
              {...stylex.props([
                styles.toggleItem,
                styles.hasCallout,
                enableTogglesV2 && itemStyle,
                enableToggles && styles.toggleItemLayout2,
                enableToggles && hasToggle && styles.mainToggleItem
              ])}
            >
              <RenderIf condition={Boolean(callout)}>
                <Container styleProp={[styles.isCallout, styles.calloutStyleProp]}>
                  <Callout
                    title={callout?.title}
                    theme={callout?.settings?.theme && toCamelCase(callout?.settings.theme) as ThemeColors}
                    styleProp={styles.badgeStyleProp}
                    size="small"
                  />
                </Container>
              </RenderIf>
              {isActive ? (
                <Container
                  as="div"
                  styleProp={[
                    styles.link,
                    isActive && styles.linkActive,
                    enableToggles && !hasToggle && [
                      styles.linkLayout2,
                      styles.linkActiveLayout2,
                    ],
                    enableToggles && hasToggle && [
                      styles.mainToggleLink,
                      styles.mainToggleActiveLink,
                    ],
                  ]}
                  onClick={() => handleToggle(index)}
                >
                  <Container
                    as="div"
                    size="3"
                    flex
                    flexRow
                    spaceBetween
                    gap="1"
                    styleProp={[
                      styles.wrapperTitleContent,
                      enableToggles && hasToggle && styles.mainToggleWrapperTitleContent,
                    ]}
                  >
                    <Typography
                      as="span"
                      typographyTheme={enableToggles && !hasToggle ? 'bodySmall' : 'bodyLarge'}
                      fontBold
                    >
                      {atfToggle?.title || product.title}
                    </Typography>
                    <RenderIf condition={enableToggles && !!hasToggle}>
                      <Container
                        as="div"
                        flex
                        flexRow
                        gap="1"
                        alignCentered
                      >
                        <Container
                          as="div"
                          flex
                          flexRow
                          gap="1"
                          styleProp={[styles.wrapperPrices]}
                        >
                          <ProductPrice
                            size="sm"
                            price={price}
                            compareAtPrice={compareAtPrice}
                            stylePropCompareAtPrice={styles.productPriceSize}
                            stylePropFinalPrice={styles.productPriceSize}
                          />
                        </Container>
                        <ArrowUpIcon
                          color={colors.navy}
                          styleProp={[
                            styles.arrowSize,
                            !toogleActive && styles.activeArrow
                          ]}
                        />
                      </Container>
                    </RenderIf>
                  </Container>
                  {enableToggles && atfToggle && hasToggle && (
                    <Container
                      as="div"
                      flex
                      flexRow
                      gap="1"
                    >
                      <RenderIf condition={callouts.length > 0}>
                        <Callouts
                          callouts={callouts as unknown as CalloutProps[]}
                        />
                      </RenderIf>
                      <RenderIf condition={Boolean(atfToggle.subheader)}>
                        <Typography
                          as="p"
                          typographyTheme="bodySmall"
                          styleProp={styles.subheaderColor}
                        >
                          {atfToggle.subheader}
                        </Typography>
                      </RenderIf>
                    </Container>
                  )}
                </Container>
              ) : (
                <ProductLink
                  product={product as any}
                  urlParams={{
                    group: addGroupParam ? group : undefined,
                  }}
                  styleProp={[
                    styles.link,
                    isActive && styles.linkActive,
                    enableToggles && !hasToggle && styles.linkLayout2,
                    enableToggles && hasToggle && [
                      styles.mainToggleLink,
                      styles.mainToggleActiveLink,
                    ],
                  ]}
                >
                  <Container
                    as="div"
                    size="3"
                    flex
                    flexRow
                    spaceBetween
                    gap="1"
                    styleProp={[
                      styles.wrapperTitleContent,
                      enableToggles && hasToggle && styles.mainToggleWrapperTitleContent,
                    ]}
                  >
                    <Typography
                      as="span"
                      typographyTheme={enableToggles && !hasToggle ? 'bodySmall' : 'bodyLarge'}
                      fontBold={enableToggles && !!hasToggle}
                    >
                      {atfToggle?.title || product.title}
                    </Typography>
                    <RenderIf condition={enableToggles && !!hasToggle}>
                      <Container
                        as="div"
                        flex
                        flexRow
                        gap="1"
                        alignCentered
                      >
                        <Container
                          as="div"
                          flex
                          flexRow
                          gap="1"
                          styleProp={[styles.wrapperPrices]}
                        >
                          <ProductPrice
                            size="sm"
                            price={price}
                            compareAtPrice={compareAtPrice}
                            stylePropCompareAtPrice={styles.productPriceSize}
                            stylePropFinalPrice={styles.productPriceSize}
                          />
                        </Container>
                        <ArrowUpIcon
                          color={colors.navy}
                          styleProp={[
                            styles.arrowSize,
                            !toogleActive && styles.activeArrow
                          ]}
                        />
                      </Container>
                    </RenderIf>
                  </Container>
                  {enableToggles && atfToggle && hasToggle && (
                    <Container
                      as="div"
                      flex
                      flexRow
                      gap="1"
                    >
                      <RenderIf condition={callouts.length > 0}>
                        <Callouts
                          callouts={callouts as unknown as CalloutProps[]}
                        />
                      </RenderIf>
                      <RenderIf condition={Boolean(atfToggle.subheader)}>
                        <Typography
                          as="p"
                          typographyTheme="bodySmall"
                          styleProp={styles.subheaderColor}
                        >
                          {atfToggle.subheader}
                        </Typography>
                      </RenderIf>
                    </Container>
                  )}
                </ProductLink>
              )}
              {enableToggles && atfToggle && hasToggle && (
                <Container
                  as="div"
                  flex
                  flexRow
                  gap="1"
                  styleProp={[
                    styles.wrapperExtraInfo,
                    toogleActive && styles.wrapperExtraInfoActive
                  ]}
                >
                  {atfToggle?.referencesCollection?.items.map((block: any) => (
                    <RenderIf condition={block.title}>
                      <Container
                        as="div"
                        flex
                        flexRow
                        gap="1"
                      >
                        <Typography
                          as="p"
                          typographyTheme="bodySmall"
                          styleProp={[
                            styles.extraInfo(
                              toCamelCase(block.settings?.theme) as keyof typeof colors
                            )
                          ]}
                        >
                          • {block.title}
                        </Typography>
                      </Container>
                    </RenderIf>
                  ))}
                </Container>
              )}
            </div>
          )
        })}
      </Container>
    </>
  )
}

export default ProductToggles
