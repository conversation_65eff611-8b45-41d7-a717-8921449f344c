/* eslint-disable */

import ProductViewedEvent from '../ProductViewedEvent'

import {
  getAllProductSlugs,
  getProductBySlug,
  extractProductDetails,
  getAllVariantsSlugs
} from '@lib/contentful/fetchProducts'
import { getProductReviewsAggregate, getProductLatestReviews } from '@lib/okendo'
import {
  getPageSections,
} from '@lib/contentful/fetchPages'
import Container from '@components/layout/Container'
import ProductDetails from '@components/Product/ProductDetails'
import OkendoReviewsWidget from '@components/Okendo/ReviewsWidget'
import PageSections from '@components/PageSections'
import Spacer from '@components/layout/Spacer'
import { generateProductRichSnippet } from '@/utils/jsonLd'
import JsonLd from '@components/Generic/JsonLd'
import { PageObjectWithSeoMetadata } from '@/app/[...slug]/page'
import fetchGlobalMetadata from '@lib/contentful/fetchGlobalMetadata'
import Dialog from '@components/Generic/Dialog'
import { MIN_RATING } from '@components/Generic/Rating'
import { generateMetaTagsWithFallBack } from '@/utils/metadata'
import { isAvailable } from '@components/Product/ProductCard/utils'
import { toCamelCase } from '@/utils/regex'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { extractProductDialogs } from '@lib/contentful/fetchGlobalDialogs'

import { notFound } from 'next/navigation'
import variantAttribute from '@/utils/variants'
import { parseAndSelectVariant } from '@/utils/urls'

export async function generateStaticParams() {
  const productSlugs = await getAllProductSlugs()
  const slugs = await getAllVariantsSlugs(productSlugs, getProductBySlug)

  // Debug: Log the generated slugs for prep-and-boards-set
  const prepAndBoardsSetSlugs = slugs.filter(s => s.slug === 'prep-and-boards-set')
  console.log('Generated variants for prep-and-boards-set:', prepAndBoardsSetSlugs)

  return slugs
}

export async function generateMetadata({ params }: { params: { slug: string } }) {
  const { slug } = params
  const [
    product,
    globalMetadata
  ] = await Promise.all<PageObjectWithSeoMetadata>([
    getProductBySlug(slug),
    fetchGlobalMetadata()
  ])

  if (!product) {
    return {
      title: 'Product not found',
      description: 'Product not found',
    }
  }

  return generateMetaTagsWithFallBack(product, globalMetadata)
}

type ProductPageProps = {
  params: {
    slug: string
    variants: string
  }
}

// eslint-disable-next-line complexity
const ProductPage = async ({ params }: ProductPageProps) => {
  const { slug } = params
  const product: any = await getProductBySlug(slug)

  if (!product) {
    notFound()
  }
 
  const { variantParams, variant: parsedVariant } = parseAndSelectVariant(
    { variants: params.variants },
    product
  )



  // Find the actual product variant that matches the parsed variant
  const selectedVariant = parsedVariant
    ? product.variants?.items?.find((v: any) => v.swatch?.slug === parsedVariant.swatch?.slug) || product.variants?.items?.[0]
    : product.variants?.items?.[0] || null

  console.log('selectedVariant', selectedVariant)

  if (!selectedVariant) {
    notFound()
  }

  // Set all variant parameters in the cache at once
  const variantSegment = Object.entries(variantParams)
    .map(([key, value]) => `${key}=${value}`)
    .join('&')
  
  if (variantSegment) {
    variantAttribute.set(variantSegment)
  }

  const [dialogs, reviewsAggregate, reviewsData] = await Promise.all([
    extractProductDialogs(product).catch(() => null),
    getProductReviewsAggregate(product.productId).catch(() => ({ rating: 0, count: 0 })),
    getProductLatestReviews(product.productId).catch(() => ({ reviews: [], aggregated: false }))
  ])
  
  const { reviews: reviewsLatest = [], aggregated = false } = reviewsData || {}
  
  const reviews = {
    aggregate: reviewsAggregate || { rating: 0, count: 0 },
    latest: reviewsLatest || [],
    aggregated: aggregated || false
  }

  const showReviews = reviews.aggregate.count > 0 && reviews.aggregate.rating >= MIN_RATING

  const details = await extractProductDetails(product)
  const sections = await getPageSections(product)

  // Remove separator section block when reviews are not shown
  if (sections.length > 0 && !showReviews) {
    const lastSection = sections[sections.length - 1]

    if (lastSection.subtype === 'Separator') {
      sections.pop()
    }
  }

  // TODO: Temp solution for anatomy module can sync the variant with the product
  sections.forEach((section: any) => {
    section.variant = selectedVariant
  })

  const group = variantParams?.group || 'default'

  return (
    <>
      <Container as="section" theme="offWhite" size="6" align="center">
        <ProductViewedEvent product={product} variant={selectedVariant} />

        <Spacer size="md" />
        <ProductDetails
          variant={selectedVariant}
          product={product as any}
          group={group}
          details={details}
          reviews={reviews}
          selectedSwatch={parsedVariant?.swatch || undefined}
        />
      </Container>
      <Spacer size="lg" />
      <PageSections sections={sections} />
      <Spacer size="lg" />
      {showReviews && (
        <Container
          as="section"
          theme="offWhite"
          size="5"
          align="center"
          id="okendo_reviews"
          styleProp={{ overflow: 'hidden' }}
        >
          <OkendoReviewsWidget productId={product.productId} />
        </Container>
      )}
      <JsonLd data={generateProductRichSnippet(product, selectedVariant, reviews)} />
      {dialogs && dialogs.map((dialog: any) => (
        <Dialog
          layout={dialog?.settings?.layout ?? 'Layout 1'}
          theme={toCamelCase(dialog?.settings?.theme) as ThemeColors}
          id={dialog.sys.id}
          key={dialog.sys.id}
          header={dialog.header}
          content={dialog.content}
          mobileContent={dialog.mobileContent}
          subheader={dialog.subheader}
          dialogToggles={dialog.togglesAndReferences?.toggles}
          compareChart={dialog?.togglesAndReferences?.compareChart}
          sections={dialog.sections}
        />
      ))}
    </>
  )
}

export default ProductPage
